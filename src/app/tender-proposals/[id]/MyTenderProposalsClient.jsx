"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import API_CONFIG from "../../../config/api";
import { useAuth } from "../../../context/AuthContext";

const TenderProposalsContainer = styled.div`
  background-color: white;
  min-height: 100vh;
  padding: 24px 20px;
`;
TenderProposalsContainer.displayName = "TenderProposalsContainer";

const BackButtonMiniSection = styled.div`
  height: 70px;
  padding: 24px 160px 12px 160px;
`;
BackButtonMiniSection.displayName = "BackButtonMiniSection";

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
ContentContainer.displayName = "ContentContainer";

const BackButton = styled.button`
  background-color: white;
  color: #434a54;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  gap: 8px;
  padding: 8px 20px;
  transition: all 0.3s ease;
  border: none;

  &:hover {
    background-color: #f8f9fa;
  }
`;
BackButton.displayName = "BackButton";

const Title = styled.h1`
  font-size: 42px;
  font-weight: 900;
  line-height: 1.5;
  color: #434a54;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 24px;
  }
`;
Title.displayName = "Title";

const SectionTitle = styled.h2`
  font-size: 24px;
  font-weight: 900;
  color: #434a54;
  margin-top: 42px;
  margin-bottom: 24px;

  &:first-of-type {
    margin-top: 0;
  }
`;
SectionTitle.displayName = "SectionTitle";

const Text = styled.p`
  font-size: 17px;
  font-weight: 400;
  color: #434a54;
  margin-bottom: 16px;
  line-height: 1.5;
`;
Text.displayName = "Text";

const ProductFormCard = styled.div`
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
`;
ProductFormCard.displayName = "ProductFormCard";

const ProductInfo = styled.div`
  flex: 1;
`;
ProductInfo.displayName = "ProductInfo";

const ProductId = styled.div`
  font-size: 17px;
  color: #969ea7;
  margin-bottom: 10px;
`;
ProductId.displayName = "ProductId";

const ProductTitle = styled.h3`
  font-size: 24px;
  font-weight: 400;
  color: #434a54;
  line-height: 32px;
  margin-bottom: 10px;
`;
ProductTitle.displayName = "ProductTitle";

const Label = styled.div`
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #969ea7;
  margin-bottom: 10px;
`;
Label.displayName = "Label";

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  height: 36px;
  padding-right: 40px;

  &:focus {
    border-color: #0066cc;
    outline: none;
  }
`;
Input.displayName = "Input";

const FormRow = styled.div`
  display: flex;
  gap: 24px;
  padding: 10px 0;
  margin-bottom: 16px;
  align-items: flex-end;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;
FormRow.displayName = "FormRow";

const SmallFormGroup = styled.div`
  display: flex;
  flex-direction: column;
  max-width: 200px;
  position: relative;
`;
SmallFormGroup.displayName = "SmallFormGroup";

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-height: 75px;
  width: 100%;
  resize: vertical;
  color: #434a54;
  margin-bottom: 8px;

  &:focus {
    outline: none;
    border-color: #0066cc;
  }
`;
TextArea.displayName = "TextArea";

const ActionButtonContainer = styled.div`
  display: flex;
  gap: 0;
  height: 36px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #0066cc;
  width: fit-content;
`;
ActionButtonContainer.displayName = "ActionButtonContainer";

const ActionButton = styled.button`
  background-color: ${(props) => (props.active ? "#0066cc" : "#f8f9fa")};
  color: ${(props) => (props.active ? "white" : "#434a54")};
  border: none;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  white-space: nowrap;
  height: 36px;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.active ? "#0056b3" : "#e9ecef")};
  }
`;
ActionButton.displayName = "ActionButton";

const DateButton = styled.button`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  background: white;
  border: 2px solid #0066cc;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  color: #0066cc;
  font-weight: 600;
  margin-top: 0px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f0f8ff;
  }
`;
DateButton.displayName = "DateButton";

const NoTenderMessage = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 16px;
`;
NoTenderMessage.displayName = "NoTenderMessage";

const LoadingMessage = styled.div`
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #666;
`;
LoadingMessage.displayName = "LoadingMessage";

const ErrorMessage = styled.div`
  text-align: center;
  padding: 40px;
  font-size: 16px;
  color: #dc3545;
`;
ErrorMessage.displayName = "ErrorMessage";

const MyTenderProposalsClient = ({ tenderId }) => {
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const [tenderInfo, setTenderInfo] = useState(null);
  const [tenderDetails, setTenderDetails] = useState(null); // Детальная информация о тендере
  const [tenderPhotos, setTenderPhotos] = useState([]); // Фотографии тендера
  const [materials, setMaterials] = useState([]);
  const [priceProposals, setPriceProposals] = useState({}); // Хранит предложения по каждому материалу
  const [companyNames, setCompanyNames] = useState({}); // Хранит названия компаний

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const purchReqId = tenderId;

  // Функция для получения материалов тендера
  const fetchTenderMaterials = async () => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqLines?purchReqId=${purchReqId}`
      );
      if (response.ok) {
        const data = await response.json();
        const materialsData = Array.isArray(data) ? data : [];
        setMaterials(materialsData);
        setTenderInfo(data); // Устанавливаем также информацию о тендере
        return materialsData;
      } else {
        throw new Error("Не удалось загрузить материалы тендера");
      }
    } catch (error) {
      console.error("Ошибка при загрузке материалов:", error);
      setError(error.message);
      return [];
    }
  };

  // Функция для получения ценовых предложений по материалу
  const fetchPriceProposals = async (purchReqLineId) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqPrices?purchReqLineId=${purchReqLineId}`
      );
      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        console.warn(
          `Не удалось загрузить предложения для материала ${purchReqLineId}`
        );
        return null;
      }
    } catch (error) {
      console.error("Ошибка при загрузке ценовых предложений:", error);
      return null;
    }
  };

  // Функция для загрузки названий компаний
  const fetchCompanyNames = async (companyIds) => {
    if (!companyIds || companyIds.length === 0) {
      return;
    }

    try {
      // Создаем массив промисов для всех запросов к компаниям
      const promises = companyIds.map(async (companyId) => {
        const response = await fetch(
          `${API_CONFIG.BASE_URL}/api/Companies/${companyId}`
        );

        if (!response.ok) {
          console.warn(
            `Ошибка загрузки компании ${companyId}: ${response.status}`
          );
          return { companyId, companyName: "Неизвестная компания" };
        }

        const companyData = await response.json();
        return {
          companyId,
          companyName: companyData.CompanyName || "Неизвестная компания",
        };
      });

      // Ждем выполнения всех запросов
      const results = await Promise.all(promises);

      // Обновляем состояние с названиями компаний
      const newCompanyNames = {};
      results.forEach(({ companyId, companyName }) => {
        newCompanyNames[companyId] = companyName;
      });

      setCompanyNames((prev) => ({ ...prev, ...newCompanyNames }));
    } catch (err) {
      console.error("Ошибка при загрузке названий компаний:", err);
    }
  };

  // Функция для загрузки фотографий тендера
  const fetchTenderPhotos = async (purchReqId) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqTablePhotos?purchReqId=${purchReqId}`
      );
      if (response.ok) {
        const data = await response.json();
        return Array.isArray(data) ? data : [];
      } else {
        console.warn(`Не удалось загрузить фотографии тендера ${purchReqId}`);
        return [];
      }
    } catch (error) {
      console.error("Ошибка при загрузке фотографий тендера:", error);
      return [];
    }
  };

  // Функция для скачивания файла
  const handleDownloadFile = (fileUrl, fileName) => {
    try {
      // Создаем временную ссылку для скачивания
      const link = document.createElement("a");
      link.href = fileUrl;
      link.download = fileName || "file";
      link.target = "_blank";

      // Добавляем ссылку в DOM, кликаем и удаляем
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Ошибка при скачивании файла:", error);
      // Fallback - открываем файл в новой вкладке
      window.open(fileUrl, "_blank");
    }
  };

  // Функция для определения типа файла
  const getFileType = (fileName) => {
    if (!fileName) return "unknown";
    const extension = fileName.toLowerCase().split(".").pop();

    const imageTypes = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"];
    const documentTypes = ["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx"];
    const archiveTypes = ["zip", "rar", "7z", "tar", "gz"];

    if (imageTypes.includes(extension)) return "image";
    if (documentTypes.includes(extension)) return "document";
    if (archiveTypes.includes(extension)) return "archive";
    return "unknown";
  };

  // Функция для получения иконки файла
  const getFileIcon = (fileName) => {
    const extension = fileName ? fileName.toLowerCase().split(".").pop() : "";

    switch (extension) {
      case "pdf":
        return "📄";
      case "doc":
      case "docx":
        return "📝";
      case "xls":
      case "xlsx":
        return "📊";
      case "ppt":
      case "pptx":
        return "📋";
      case "zip":
      case "rar":
      case "7z":
        return "📦";
      case "txt":
        return "📃";
      default:
        return "📎";
    }
  };

  // Функция для получения детальной информации о тендере
  const fetchTenderDetails = async (purchReqId) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqTables/${purchReqId}`
      );
      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        console.warn(`Не удалось загрузить детали тендера ${purchReqId}`);
        return null;
      }
    } catch (error) {
      console.error("Ошибка при загрузке деталей тендера:", error);
      return null;
    }
  };

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth");
      return;
    }

    const loadData = async () => {
      if (!purchReqId) {
        setError("ID тендера не указан");
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Загружаем данные параллельно
        const [tenderDetailsData, materialsData, tenderPhotosData] =
          await Promise.all([
            fetchTenderDetails(purchReqId),
            fetchTenderMaterials(),
            fetchTenderPhotos(purchReqId),
          ]);

        setTenderDetails(tenderDetailsData);
        setTenderPhotos(tenderPhotosData);

        // Загружаем ценовые предложения для каждого материала
        if (materialsData && materialsData.length > 0) {
          const proposalsPromises = materialsData.map(async (material) => {
            const proposals = await fetchPriceProposals(
              material.PurchReqLineId
            );
            return {
              purchReqLineId: material.PurchReqLineId,
              proposals: proposals,
            };
          });

          const proposalsResults = await Promise.all(proposalsPromises);

          // Создаем объект с предложениями по каждому материалу
          const proposalsData = {};
          const allCompanyIds = new Set();

          proposalsResults.forEach(({ purchReqLineId, proposals }) => {
            proposalsData[purchReqLineId] = proposals;

            // Собираем все CompanyId из предложений
            if (proposals && Array.isArray(proposals)) {
              proposals.forEach((proposal) => {
                if (proposal.CompanyId) {
                  allCompanyIds.add(proposal.CompanyId);
                }
              });
            }
          });

          setPriceProposals(proposalsData);

          // Загружаем названия компаний
          if (allCompanyIds.size > 0) {
            fetchCompanyNames(Array.from(allCompanyIds));
          }
        }
      } catch (err) {
        setError(err.message);
        console.error("Ошибка при загрузке данных:", err);
      } finally {
        setIsLoading(false);
      }
    };

    if (purchReqId) {
      loadData();
    }
  }, [purchReqId, isAuthenticated, router]);

  const handleBack = () => {
    router.push("/my-tenders");
  };

  if (isLoading) {
    return (
      <TenderProposalsContainer>
        <ContentContainer>
          <LoadingMessage>Загрузка ценовых предложений...</LoadingMessage>
        </ContentContainer>
      </TenderProposalsContainer>
    );
  }

  if (error) {
    return (
      <TenderProposalsContainer>
        <ContentContainer>
          <ErrorMessage>Ошибка: {error}</ErrorMessage>
        </ContentContainer>
      </TenderProposalsContainer>
    );
  }

  if (!tenderInfo) {
    return (
      <TenderProposalsContainer>
        <ContentContainer>
          <NoTenderMessage>Тендер не найден</NoTenderMessage>
        </ContentContainer>
      </TenderProposalsContainer>
    );
  }

  return (
    <TenderProposalsContainer>
      <BackButtonMiniSection>
        <BackButton onClick={handleBack}>
          <img
            src="/icons/arrow_back_24px.svg"
            alt="Назад"
            style={{ width: "12px", height: "12px" }}
          />{" "}
          Назад к моим тендерам
        </BackButton>
      </BackButtonMiniSection>

      <ContentContainer>
        <Title>Ценовые предложения по тендеру</Title>

        {/* Информация о тендере */}
        {tenderDetails && (
          <ProductFormCard
            style={{ marginBottom: "24px", backgroundColor: "#f0f8ff" }}
          >
            <ProductInfo>
              <ProductTitle>{tenderDetails.PurchReqName}</ProductTitle>
              <Label>
                Дата окончания:{" "}
                {tenderDetails.PurchEndDate
                  ? new Date(tenderDetails.PurchEndDate).toLocaleDateString(
                      "ru-RU"
                    )
                  : "Не указано"}
              </Label>
              <Label>
                Адрес доставки: {tenderDetails.DeliveryAddress || "Не указан"}
              </Label>
              {tenderDetails.Description && (
                <Label>Описание: {tenderDetails.Description}</Label>
              )}
            </ProductInfo>
          </ProductFormCard>
        )}

        {/* Фотографии тендера */}
        {tenderPhotos && tenderPhotos.length > 0 && (
          <ProductFormCard style={{ marginBottom: "24px" }}>
            <ProductInfo>
              <ProductTitle>Прикрепленные файлы</ProductTitle>
              <div
                style={{
                  display: "flex",
                  flexWrap: "wrap",
                  gap: "12px",
                  marginTop: "12px",
                }}
              >
                {tenderPhotos.map((photo, index) => {
                  const fileType = getFileType(photo.FileName);
                  const isImage = fileType === "image";

                  return (
                    <div
                      key={photo.PurchReqTableFotoId}
                      style={{ position: "relative", cursor: "pointer" }}
                      onClick={() =>
                        handleDownloadFile(photo.FileUrl, photo.FileName)
                      }
                      title={`Скачать ${photo.FileName}`}
                    >
                      {isImage ? (
                        <img
                          src={photo.FileUrl}
                          alt={`Фото тендера ${index + 1}`}
                          style={{
                            width: "150px",
                            height: "150px",
                            objectFit: "cover",
                            borderRadius: "4px",
                            border: "1px solid #ddd",
                            transition: "opacity 0.2s",
                          }}
                          onError={(e) => {
                            e.target.style.display = "none";
                          }}
                          onMouseEnter={(e) => {
                            e.target.style.opacity = "0.8";
                          }}
                          onMouseLeave={(e) => {
                            e.target.style.opacity = "1";
                          }}
                        />
                      ) : (
                        <div
                          style={{
                            width: "150px",
                            height: "150px",
                            borderRadius: "4px",
                            border: "1px solid #ddd",
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: "#f8f9fa",
                            transition: "opacity 0.2s",
                          }}
                          onMouseEnter={(e) => {
                            e.target.style.opacity = "0.8";
                          }}
                          onMouseLeave={(e) => {
                            e.target.style.opacity = "1";
                          }}
                        >
                          <div
                            style={{ fontSize: "48px", marginBottom: "8px" }}
                          >
                            {getFileIcon(photo.FileName)}
                          </div>
                          <div
                            style={{
                              fontSize: "10px",
                              textAlign: "center",
                              padding: "0 4px",
                              color: "#666",
                              wordBreak: "break-word",
                            }}
                          >
                            {photo.FileName}
                          </div>
                        </div>
                      )}

                      {isImage && (
                        <div
                          style={{
                            position: "absolute",
                            bottom: "4px",
                            left: "4px",
                            right: "4px",
                            background: "rgba(0,0,0,0.7)",
                            color: "white",
                            fontSize: "10px",
                            padding: "2px 4px",
                            borderRadius: "2px",
                            textAlign: "center",
                            pointerEvents: "none",
                          }}
                        >
                          {photo.FileName}
                        </div>
                      )}

                      {/* Иконка скачивания */}
                      <div
                        style={{
                          position: "absolute",
                          top: "4px",
                          right: "4px",
                          background: "rgba(0,0,0,0.7)",
                          color: "white",
                          borderRadius: "50%",
                          width: "24px",
                          height: "24px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          fontSize: "12px",
                          pointerEvents: "none",
                        }}
                      >
                        ⬇
                      </div>
                    </div>
                  );
                })}
              </div>
            </ProductInfo>
          </ProductFormCard>
        )}

        <SectionTitle>Материалы тендера</SectionTitle>
        {materials.map((material) => {
          const proposalsArray = priceProposals[material.PurchReqLineId];

          return (
            <div key={material.MaterialId}>
              {/* Информация о материале */}
              <ProductFormCard
                style={{ marginBottom: "8px", backgroundColor: "#f0f8ff" }}
              >
                <ProductInfo>
                  <ProductId>{material.MaterialId}</ProductId>
                  <Label>Единица измерения: {material.PurchUnit}</Label>
                  <ProductTitle>{material.MaterialName}</ProductTitle>
                  <Label style={{ marginBottom: 16 }}>
                    {material.PurchQty} {material.PurchUnit}
                  </Label>
                  <Label style={{ marginBottom: 16 }}>
                    Начальная цена заказчика:&nbsp;
                    {(material.PurchOpenPrice ?? " Цена не указана") +
                      (material.PurchOpenPrice
                        ? ` ₸ за ${material.PurchUnit}`
                        : "")}
                  </Label>
                  <Label style={{ marginBottom: 16 }}>
                    Комментарии заказчика: {material.Description}
                  </Label>

                  {/* <Label style={{ marginBottom: 16 }}>
                    Дата актуальности предложения: до{" "}
                    {tenderDetails?.PurchEndDate
                      ? new Date(tenderDetails.PurchEndDate).toLocaleDateString(
                          "ru-RU"
                        )
                      : "Не указано"}
                  </Label> */}
                </ProductInfo>
              </ProductFormCard>

              {/* Ценовые предложения для этого материала */}
              {proposalsArray && proposalsArray.length > 0 ? (
                proposalsArray.map((proposal, proposalIndex) => (
                  <ProductFormCard
                    key={`${material.MaterialId}-${proposalIndex}`}
                    style={{ marginBottom: "16px" }}
                  >
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        marginBottom: "16px",
                        padding: "8px 12px",
                        backgroundColor: "#e8f4fd",
                        borderRadius: "4px",
                      }}
                    >
                      <Label
                        style={{
                          fontSize: "16px",
                          fontWeight: "600",
                          color: "#0066cc",
                          margin: 0,
                        }}
                      >
                        Ценовое предложение №{proposalIndex + 1}
                      </Label>
                      <Label
                        style={{
                          fontSize: "14px",
                          color: "#666",
                          margin: 0,
                        }}
                      >
                        от:{" "}
                        {companyNames[proposal.CompanyId] ||
                          "Загрузка компании..."}
                      </Label>
                    </div>

                    <FormRow>
                      <SmallFormGroup>
                        <Input
                          type="text"
                          value={
                            proposal.OfferPrice
                              ? proposal.OfferPrice.toString()
                              : "Не указано"
                          }
                          placeholder="Ваша цена"
                          disabled
                          style={{ backgroundColor: "#f8f9fa", color: "#666" }}
                        />
                        <span
                          style={{
                            position: "absolute",
                            right: "8px",
                            bottom: "8px",
                            color: "#656D78",
                            fontSize: "14px",
                          }}
                        >
                          ₸ за {material.PurchUnit}
                        </span>
                      </SmallFormGroup>

                      <ActionButtonContainer
                        style={{ border: "1px solid #f0f8ff" }}
                      >
                        <ActionButton
                          active={proposal.PriceWithDelivery === true}
                        >
                          Цена с доставкой
                        </ActionButton>
                        <ActionButton
                          active={proposal.PriceWithDelivery === false}
                        >
                          Без
                        </ActionButton>
                      </ActionButtonContainer>

                      <ActionButtonContainer
                        style={{ border: "1px solid #f0f8ff" }}
                      >
                        <ActionButton active={proposal.IsOriginal === true}>
                          Соответствует запросу
                        </ActionButton>
                        <ActionButton active={proposal.IsOriginal === false}>
                          Аналог
                        </ActionButton>
                      </ActionButtonContainer>
                    </FormRow>

                    <Label style={{ fontSize: "17px", color: "#656D78" }}>
                      Комментарий к предложению
                    </Label>

                    <TextArea
                      value={proposal.Description || "Комментарий не указан"}
                      placeholder="Комментарий к предложению"
                      disabled
                      style={{ backgroundColor: "#f8f9fa", color: "#666" }}
                    />

                    <DateButton
                      disabled
                      style={{
                        backgroundColor: "#f8f9fa",
                        color: "#666",
                        borderColor: "#ddd",
                      }}
                    >
                      Ценовое предложение актуально до:{" "}
                      {proposal.ActualDate
                        ? new Date(proposal.ActualDate).toLocaleDateString(
                            "ru-RU"
                          )
                        : "Не указано"}
                      <img src="/icons/Cell/Vector.svg" alt="Календарь" />
                    </DateButton>
                  </ProductFormCard>
                ))
              ) : (
                <ProductFormCard style={{ marginBottom: "16px" }}>
                  <div
                    style={{
                      textAlign: "center",
                      padding: "20px",
                      color: "#666",
                      fontStyle: "italic",
                    }}
                  >
                    Ценовых предложений для этого материала пока нет
                  </div>
                </ProductFormCard>
              )}
            </div>
          );
        })}
      </ContentContainer>
    </TenderProposalsContainer>
  );
};

export default MyTenderProposalsClient;
