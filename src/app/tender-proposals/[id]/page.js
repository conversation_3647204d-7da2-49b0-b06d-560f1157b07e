import Layout from "../../../components/Layout";
import MyTenderProposalsClient from "./MyTenderProposalsClient";

export async function generateMetadata({ params }) {
  const { id } = params;

  return {
    title: `Ценовые предложения по тендеру ${id} | SADI Shop - строительные материалы в Астане, Казахстан`,
    description: `Просмотр ценовых предложений по тендеру ${id}. Анализ поступивших предложений от поставщиков строительных материалов через SADI Shop в Астане, Казахстан.`,
    keywords:
      "тендер, ценовые предложения, строительные материалы, поставщики, <PERSON>DI, <PERSON><PERSON><PERSON><PERSON><PERSON>, Казахстан",
    openGraph: {
      title: `Ценовые предложения по тендеру ${id} | SADI Shop`,
      description: `Просмотр ценовых предложений по тендеру ${id}. Анализ поступивших предложений от поставщиков строительных материалов.`,
      type: "website",
      locale: "ru_RU",
    },
  };
}

export default function TenderProposalsPage({ params }) {
  return (
    <Layout>
      <MyTenderProposalsClient tenderId={params.id} />
    </Layout>
  );
}
