"use client";

import React, { useState, useEffect } from "react";
import styled from "styled-components";
import API_CONFIG from "../../config/api";
import { useAuth } from "../../context/AuthContext";

const FindTenderContainer = styled.div``;
FindTenderContainer.displayName = "FindTenderContainer";

const Title = styled.h1`
  font-size: 42px;
  font-weight: 900;
  line-height: 1.5;
  text-align: center;
  margin-top: 47px;
  color: #434a54;

  @media (max-width: 768px) {
    font-size: 24px;
  }
`;
Title.displayName = "Title";

const FormContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  margin-top: 28px;
  background-color: white;
  padding: 12px;
`;
FormContainer.displayName = "FormContainer";

const TabsContainer = styled.div`
  display: flex;
  max-width: 800px;
  margin: 0 auto;
  overflow-x: auto;

  @media (max-width: 768px) {
    gap: 0;
  }
`;
TabsContainer.displayName = "TabsContainer";

const Tab = styled.button`
  padding: 16px 24px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  color: #6c757d;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s ease;

  ${(props) =>
    props.active &&
    `
    color: #0066cc;
    border-bottom-color: #0066cc;
  `}

  &:hover {
    color: #0066cc;
  }

  @media (max-width: 768px) {
    padding: 12px 16px;
    font-size: 13px;
  }
`;
Tab.displayName = "Tab";

const SearchSection = styled.div`
  display: flex;
  gap: 15px;
  margin-top: 25px;
  flex-wrap: wrap;
`;

const Select = styled.select`
  padding: 14px 20px;
  font-size: 16px;
  border: 1px solid #dcdcdc;
  border-radius: 6px;
  flex: 1;
  min-width: 240px;
`;

const InputWrapper = styled.div`
  display: flex;
  flex: 2;
  min-width: 300px;
  border: 1px solid #dcdcdc;
  border-radius: 6px;
  overflow: hidden;
`;

const Input = styled.input`
  flex: 1;
  padding: 14px 20px;
  border: none;
  font-size: 16px;
  outline: none;

  &::placeholder {
    color: #b0b0b0;
  }
`;

const SearchButton = styled.button`
  width: 60px;
  background-color: #0064d9;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
`;

const Filters = styled.div`
  display: flex;
  gap: 30px;
  margin-top: 20px;
  flex-wrap: wrap;
`;

const CheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #5d6670;

  input {
    margin-right: 10px;
    width: 16px;
    height: 16px;
  }
`;

const CitySelector = styled.div`
  flex: 1;
  min-width: 240px;
`;
CitySelector.displayName = "CitySelector";

const CityDropdown = styled.div`
  position: relative;
  width: 100%;
`;
CityDropdown.displayName = "CityDropdown";

const CityButton = styled.button`
  width: 100%;
  height: 52px;
  background: white;
  border: 1px solid #dcdcdc;
  border-radius: 6px;
  padding: 14px 20px;
  font-size: 16px;
  color: #333;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: left;

  &:hover {
    border-color: #bbb;
  }

  &:focus {
    outline: none;
    border-color: #0066cc;
  }

  &:after {
    content: "▼";
    font-size: 12px;
    color: #666;
    transition: transform 0.2s ease;
    transform: ${(props) => (props.isOpen ? "rotate(180deg)" : "rotate(0deg)")};
  }
`;
CityButton.displayName = "CityButton";

const CityDropdownList = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  width: 200px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
`;
CityDropdownList.displayName = "CityDropdownList";

const CityOption = styled.div`
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
`;
CityOption.displayName = "CityOption";

const CityCheckbox = styled.div`
  width: 20px;
  height: 20px;
  border: 2px solid ${(props) => (props.checked ? "#0066cc" : "#e0e0e0")};
  border-radius: 4px;
  background: ${(props) => (props.checked ? "#0066cc" : "white")};
  display: flex;
  align-items: center;
  justify-content: center;

  &:after {
    content: "✓";
    color: white;
    font-size: 14px;
    font-weight: bold;
    display: ${(props) => (props.checked ? "block" : "none")};
  }
`;
CityCheckbox.displayName = "CityCheckbox";

// Стили для результатов поиска тендеров (точно как в CreateTenderClient)
const ContentSection = styled.div`
  background-color: white;
  flex-grow: 1;
  padding: 10px 20px;
  min-height: 46vh;
  margin-top: 20px;

  @media (max-width: 768px) {
    padding: 24px 16px;
  }
`;
ContentSection.displayName = "ContentSection";

const TenderResultsContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
TenderResultsContainer.displayName = "TenderResultsContainer";

const TenderResultsGrid = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 20px;
  transition: opacity 0.3s ease-in-out;

  /* Анимация появления карточек */
  & > * {
    animation: fadeInUp 0.4s ease-out;
    animation-fill-mode: both;
  }

  /* Задержки для плавного появления карточек по очереди */
  & > *:nth-child(1) {
    animation-delay: 0.05s;
  }
  & > *:nth-child(2) {
    animation-delay: 0.1s;
  }
  & > *:nth-child(3) {
    animation-delay: 0.15s;
  }
  & > *:nth-child(4) {
    animation-delay: 0.2s;
  }
  & > *:nth-child(5) {
    animation-delay: 0.25s;
  }
  & > *:nth-child(6) {
    animation-delay: 0.3s;
  }
  & > *:nth-child(7) {
    animation-delay: 0.35s;
  }
  & > *:nth-child(8) {
    animation-delay: 0.4s;
  }
  & > *:nth-child(9) {
    animation-delay: 0.45s;
  }
  & > *:nth-child(10) {
    animation-delay: 0.5s;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;
TenderResultsGrid.displayName = "TenderResultsGrid";

const TenderCard = styled.div`
  background-color: #e8e8e8;
  border-radius: 4px;
  padding: 20px;
  border: none;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;
TenderCard.displayName = "TenderCard";

const TenderTitle = styled.h3`
  font-size: 24px;
  font-weight: 900;
  color: #434a54;
  margin: 0 0 8px 0;
`;
TenderTitle.displayName = "TenderTitle";

const TenderSubtitle = styled.div`
  font-size: 14px;
  color: #434a54;
  margin-bottom: 4px;
  font-weight: 700;
  text-transform: uppercase;
`;
TenderSubtitle.displayName = "TenderSubtitle";

const TenderDeadline = styled.div`
  font-size: 14px;
  font-weight: 700;
  color: #434a54;
  margin-bottom: 16px;
  text-transform: uppercase;
`;
TenderDeadline.displayName = "TenderDeadline";

// const ResultsCounter = styled.div`
//   color: #6c757d;
//   font-size: 14px;
//   margin-bottom: 16px;
// `;
// ResultsCounter.displayName = "ResultsCounter";

const NoResultsMessage = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
  font-size: 16px;
`;
NoResultsMessage.displayName = "NoResultsMessage";

// Стили для пагинации
const PaginationContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 32px;
  padding: 20px 0;
`;
PaginationContainer.displayName = "PaginationContainer";

const PaginationButton = styled.button`
  width: 40px;
  height: 40px;
  border: 1px solid #e0e0e0;
  background-color: ${(props) => (props.active ? "#1976d2" : "white")};
  color: ${(props) => (props.active ? "white" : "#333")};
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled) {
    background-color: ${(props) => (props.active ? "#1565c0" : "#f5f5f5")};
    border-color: ${(props) => (props.active ? "#1565c0" : "#ccc")};
  }

  &:disabled {
    background-color: #f5f5f5;
    color: #ccc;
    cursor: not-allowed;
    border-color: #e0e0e0;
  }
`;
PaginationButton.displayName = "PaginationButton";

const PaginationDots = styled.span`
  color: #666;
  font-size: 14px;
  padding: 0 4px;
`;
PaginationDots.displayName = "PaginationDots";

// Стили для материалов тендера
const MaterialsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;
MaterialsList.displayName = "MaterialsList";

const MaterialItem = styled.div`
  font-size: 14px;
  line-height: 1.4;
  padding-bottom: 10px; /* смещает border вниз */
  border-bottom: 1px solid #919399;
`;
MaterialItem.displayName = "MaterialItem";

const MaterialId = styled.div`
  color: #969ea7;
  font-size: 14px;
  font-weight: 400;
  text-transform: uppercase;
  margin-bottom: 2px;
`;
MaterialId.displayName = "MaterialId";

const MaterialUnit = styled.div`
  color: #969ea7;
  font-size: 14px;
  margin-bottom: 4px;
`;
MaterialUnit.displayName = "MaterialUnit";

const MaterialName = styled.div`
  color: #434a54;
  font-size: 17px;
  font-weight: 400;
`;
MaterialName.displayName = "MaterialName";

const MaterialQty = styled.div`
  color: #434a54;
  font-size: 17px;
  font-weight: 400;
`;
MaterialQty.displayName = "MaterialQty";

const MaterialOpenPrice = styled.div`
  color: #434a54;
  font-size: 17px;
  font-weight: 400;
`;
MaterialOpenPrice.displayName = "MaterialOpenPrice";

const MaterialsLoading = styled.div`
  color: #666;
  font-size: 14px;
  font-style: italic;
  padding: 8px 0;
`;
MaterialsLoading.displayName = "MaterialsLoading";

const NoMaterials = styled.div`
  color: #666;
  font-size: 14px;
  font-style: italic;
  padding: 8px 0;
`;
NoMaterials.displayName = "NoMaterials";

const ViewTenderButton = styled.button`
  background-color: transparent;
  border: 2px solid #0066cc;
  color: #0066cc;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f0f8ff;
  }
`;
ViewTenderButton.displayName = "ViewTenderButton";

const tabs = [
  { id: "all", label: "ВСЕ ТЕНДЕРЫ" },
  { id: "materials", label: "СТРОИТЕЛЬНЫЕ МАТЕРИАЛЫ" },
  { id: "equipment", label: "ОБОРУДОВАНИЕ" },
  { id: "tools", label: "ИНСТРУМЕНТЫ" },
];

function FindTenderClient() {
  const { isAuthenticated } = useAuth();
  const [activeTab, setActiveTab] = useState("all");
  const [selectedCity, setSelectedCity] = useState("Весь Казахстан");
  const [selectedRegionId, setSelectedRegionId] = useState(null);
  const cities = [
    { RegionId: null, RegionName: "Весь Казахстан" },
    { RegionId: "01", RegionName: "Нур-Султан" },
    { RegionId: "02", RegionName: "Алматы" },
    { RegionId: "04", RegionName: "Актюбинская область" },
    { RegionId: "05", RegionName: "Алматинская область" },
    { RegionId: "06", RegionName: "Атырауская область" },
    { RegionId: "07", RegionName: "Западно-Казахстанская область" },
    { RegionId: "08", RegionName: "Жамбылская область" },
    { RegionId: "09", RegionName: "Карагандинская область" },
    { RegionId: "10", RegionName: "Костанайская область" },
    { RegionId: "11", RegionName: "Кызылординская область" },
    { RegionId: "12", RegionName: "Мангистауская область" },
    { RegionId: "13", RegionName: "Туркестанская область" },
    { RegionId: "14", RegionName: "Павлодарская область" },
    { RegionId: "15", RegionName: "Северо-Казахстанская область" },
    { RegionId: "16", RegionName: "Восточно-Казахстанская область" },
    { RegionId: "17", RegionName: "Акмолинская область" },
    { RegionId: "3 ", RegionName: "Шымкент" },
  ];
  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [tenders, setTenders] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Состояние для материалов тендеров
  const [tenderMaterials, setTenderMaterials] = useState({});
  const [isMaterialsLoading, setIsMaterialsLoading] = useState(false);

  // Состояние для названий компаний
  const [companyNames, setCompanyNames] = useState({});

  // Состояние для пагинации
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 2; // 2 карточки на страницу

  // Функция для загрузки тендеров
  const fetchTenders = async (
    isSpecification = false,
    searchQuery = "",
    regionId = null
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      let url = `${API_CONFIG.BASE_URL}/api/PurchReqTables?isSpecification=${isSpecification}`;

      if (regionId) {
        url += `&regionId=${regionId}`;
      }

      if (searchQuery.trim()) {
        url += `&searchText=${encodeURIComponent(searchQuery)}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Ошибка загрузки: ${response.status}`);
      }

      const data = await response.json();
      setTenders(Array.isArray(data) ? data : []);
    } catch (err) {
      setError(err.message);
      setTenders([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Функция для загрузки названий компаний
  const fetchCompanyNames = async (companyIds) => {
    if (!companyIds || companyIds.length === 0) {
      return;
    }

    try {
      // Создаем массив промисов для всех запросов к компаниям
      const promises = companyIds.map(async (companyId) => {
        const response = await fetch(
          `${API_CONFIG.BASE_URL}/api/Companies/${companyId}`
        );

        if (!response.ok) {
          console.warn(
            `Ошибка загрузки компании ${companyId}: ${response.status}`
          );
          return { companyId, companyName: "Неизвестная компания" };
        }

        const companyData = await response.json();
        return {
          companyId,
          companyName: companyData.CompanyName || "Неизвестная компания",
        };
      });

      // Ждем выполнения всех запросов
      const results = await Promise.all(promises);

      // Обновляем состояние с названиями компаний
      const newCompanyNames = {};
      results.forEach(({ companyId, companyName }) => {
        newCompanyNames[companyId] = companyName;
      });

      setCompanyNames((prev) => ({ ...prev, ...newCompanyNames }));
    } catch (err) {
      console.error("Ошибка при загрузке названий компаний:", err);
    }
  };

  // Функция для загрузки материалов тендеров
  const fetchTenderMaterials = async (purchReqIds) => {
    if (!purchReqIds || purchReqIds.length === 0) {
      return;
    }

    setIsMaterialsLoading(true);

    try {
      // Создаем массив промисов для всех запросов
      const promises = purchReqIds.map(async (purchReqId) => {
        const response = await fetch(
          `${API_CONFIG.BASE_URL}/api/PurchReqLines?purchReqId=${purchReqId}`
        );

        if (!response.ok) {
          console.warn(
            `Ошибка загрузки материалов для тендера ${purchReqId}: ${response.status}`
          );
          return { purchReqId, materials: [] };
        }

        const materials = await response.json();
        return {
          purchReqId,
          materials: Array.isArray(materials) ? materials : [],
        };
      });

      // Ждем выполнения всех запросов
      const results = await Promise.all(promises);

      // Формируем объект с материалами по purchReqId
      const materialsMap = {};
      results.forEach(({ purchReqId, materials }) => {
        materialsMap[purchReqId] = materials;
      });

      setTenderMaterials(materialsMap);
    } catch (err) {
      console.error("Ошибка загрузки материалов тендеров:", err);
      setTenderMaterials({});
    } finally {
      setIsMaterialsLoading(false);
    }
  };

  // Убираем useEffect для смены вкладок - теперь это только UI без запросов к API

  // Логика пагинации
  const totalPages = Math.ceil(tenders.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentTenders = tenders.slice(startIndex, endIndex);

  // Загрузка тендеров при первом рендере
  useEffect(() => {
    fetchTenders(false, "", selectedRegionId); // По умолчанию загружаем строительные материалы для всего Казахстана
  }, []);

  // Загрузка материалов и названий компаний при смене страницы или изменении тендеров
  useEffect(() => {
    if (currentTenders.length > 0) {
      const purchReqIds = currentTenders.map((tender) => tender.PurchReqId);
      const companyIds = currentTenders
        .map((tender) => tender.CompanyId)
        .filter(Boolean);

      fetchTenderMaterials(purchReqIds);
      fetchCompanyNames(companyIds);
    }
  }, [currentPage, tenders]);

  const handleCitySelect = (cityObj) => {
    setSelectedCity(cityObj.RegionName);
    setSelectedRegionId(cityObj.RegionId);
    setIsCityDropdownOpen(false);
    // Загружаем тендеры для выбранного региона
    fetchTenders(false, searchText, cityObj.RegionId);
  };

  const toggleCityDropdown = () => {
    setIsCityDropdownOpen(!isCityDropdownOpen);
  };

  const handleSearch = () => {
    const isSpecification = false; // Всегда false
    fetchTenders(isSpecification, searchText, selectedRegionId);
  };

  const handleSearchInputChange = (e) => {
    setSearchText(e.target.value);
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleSearchWithReset();
    }
  };

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    // Убираем очистку поиска - теперь вкладки только для UI
  };

  // Функция для форматирования даты в формате DD.MM.YY
  const formatDate = (dateString) => {
    if (!dateString) return "Не указано";
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear().toString().slice(-2);
    return `${day}.${month}.${year}`;
  };

  // Функции для пагинации
  const handlePageChange = (page) => {
    setCurrentPage(page);
    // Прокручиваем к началу результатов
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  };

  // Функция для генерации номеров страниц для отображения
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Если страниц мало, показываем все
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Если страниц много, показываем с многоточием
      if (currentPage <= 3) {
        // Начало: 1, 2, 3, ..., last
        pages.push(1, 2, 3, "...", totalPages);
      } else if (currentPage >= totalPages - 2) {
        // Конец: 1, ..., last-2, last-1, last
        pages.push(1, "...", totalPages - 2, totalPages - 1, totalPages);
      } else {
        // Середина: 1, ..., current-1, current, current+1, ..., last
        pages.push(
          1,
          "...",
          currentPage - 1,
          currentPage,
          currentPage + 1,
          "...",
          totalPages
        );
      }
    }

    return pages;
  };

  // Сброс на первую страницу при новом поиске
  const handleSearchWithReset = () => {
    setCurrentPage(1);
    handleSearch();
  };

  // Функция для перехода к просмотру тендера
  const handleViewTender = (purchReqId) => {
    // Проверяем авторизацию
    if (!isAuthenticated) {
      window.location.href = "/auth?from=tender";
      return;
    }

    // Сохраняем только базовую информацию о тендере в localStorage (как fallback)
    const tenderData = tenders.find(
      (tender) => tender.PurchReqId === purchReqId
    );

    if (tenderData) {
      try {
        // Сохраняем только основную информацию, материалы будут загружены через API
        localStorage.setItem("selectedTenderInfo", JSON.stringify(tenderData));
        // Переходим на страницу подачи предложения
        window.location.href = `/tender-proposal/${purchReqId}`;
      } catch (error) {
        console.error("Ошибка при сохранении данных тендера:", error);
        // Если localStorage не работает, все равно переходим на страницу
        window.location.href = `/tender-proposal/${purchReqId}`;
      }
    }
  };

  return (
    <FindTenderContainer>
      <Title>
        Находите тендеры на поставку строительных <br /> материалов,
        оборудования, инструментов
      </Title>

      <FormContainer>
        <TabsContainer>
          {tabs.map((tab) => (
            <Tab
              key={tab.id}
              active={activeTab === tab.id}
              onClick={() => handleTabChange(tab.id)}
            >
              {tab.label}
            </Tab>
          ))}
        </TabsContainer>

        <SearchSection>
          <CitySelector>
            <CityDropdown>
              <CityButton
                onClick={toggleCityDropdown}
                isOpen={isCityDropdownOpen}
              >
                {selectedCity}
              </CityButton>
              {isCityDropdownOpen && (
                <CityDropdownList>
                  {cities.map((city) => (
                    <CityOption
                      key={city.RegionId}
                      onClick={() => handleCitySelect(city)}
                    >
                      <span>{city.RegionName}</span>
                      <CityCheckbox
                        checked={selectedCity === city.RegionName}
                      />
                    </CityOption>
                  ))}
                </CityDropdownList>
              )}
            </CityDropdown>
          </CitySelector>

          <InputWrapper>
            <Input
              placeholder="По названию материала"
              value={searchText}
              onChange={handleSearchInputChange}
              onKeyDown={handleKeyDown}
            />
            <SearchButton onClick={handleSearchWithReset}>
              {/* Вставьте сюда иконку поиска */}
              <img src="/icons/Union.svg" alt="Поиск" />
            </SearchButton>
          </InputWrapper>
        </SearchSection>

        <Filters>
          <CheckboxLabel>
            <input type="checkbox" />
            Закупки без предложений
          </CheckboxLabel>
          <CheckboxLabel>
            <input type="checkbox" />
            Закупки созданные юр.лицом
          </CheckboxLabel>
        </Filters>
      </FormContainer>

      {/* Результаты поиска тендеров */}
      <ContentSection>
        <TenderResultsContainer>
          {/* Счетчик результатов
          {!isLoading && !error && tenders.length > 0 && (
            <ResultsCounter>
              {tenders.length === 0
                ? "Ничего не найдено"
                : `Найдено: ${tenders.length} ${
                    tenders.length === 1
                      ? "тендер"
                      : tenders.length < 5
                      ? "тендера"
                      : "тендеров"
                  }`}
            </ResultsCounter>
          )} */}

          {isLoading ? (
            <NoResultsMessage>Загрузка тендеров...</NoResultsMessage>
          ) : error ? (
            <NoResultsMessage>Ошибка загрузки: {error}</NoResultsMessage>
          ) : tenders.length === 0 ? (
            <NoResultsMessage>
              {searchText.trim()
                ? `По запросу "${searchText}" тендеры не найдены`
                : "Тендеры не найдены"}
            </NoResultsMessage>
          ) : (
            <TenderResultsGrid>
              {currentTenders.map((tender, index) => {
                const materialsCount = tenderMaterials[tender.PurchReqId]
                  ? tenderMaterials[tender.PurchReqId].length
                  : 0;

                return (
                  <TenderCard
                    key={tender.PurchReqId}
                    style={{
                      animationDelay: `${index * 0.05}s`,
                    }}
                  >
                    <TenderTitle>{tender.PurchReqName}</TenderTitle>
                    <TenderSubtitle>
                      {materialsCount > 0
                        ? `${materialsCount} ${
                            materialsCount === 1
                              ? "позиция"
                              : materialsCount < 5
                              ? "позиции"
                              : "позиций"
                          } в тендере`
                        : "Загрузка позиций..."}
                    </TenderSubtitle>
                    <TenderSubtitle>
                      {companyNames[tender.CompanyId] || "Загрузка компании..."}
                    </TenderSubtitle>
                    <TenderDeadline>
                      Поставить до: {formatDate(tender.PurchEndDate)}
                    </TenderDeadline>

                    {/* Материалы тендера */}
                    {isMaterialsLoading ? (
                      <MaterialsLoading>
                        Загрузка материалов...
                      </MaterialsLoading>
                    ) : tenderMaterials[tender.PurchReqId] &&
                      tenderMaterials[tender.PurchReqId].length > 0 ? (
                      <MaterialsList>
                        {tenderMaterials[tender.PurchReqId]
                          .slice(0, 3)
                          .map((material) => (
                            <MaterialItem key={material.PurchReqLineId}>
                              <MaterialId>{material.MaterialId}</MaterialId>
                              <MaterialUnit>
                                Единица измерения: {material.PurchUnit}
                              </MaterialUnit>
                              <MaterialName>
                                {material.MaterialName}
                              </MaterialName>
                              <MaterialQty>
                                {material.PurchQty} {material.PurchUnit}
                              </MaterialQty>
                              <MaterialOpenPrice>
                                {(material.PurchOpenPrice ??
                                  "Цена не указана") +
                                  (material.PurchOpenPrice
                                    ? ` ₸ за ${material.PurchUnit}`
                                    : "")}
                              </MaterialOpenPrice>
                            </MaterialItem>
                          ))}
                      </MaterialsList>
                    ) : (
                      <NoMaterials>Материалы не найдены</NoMaterials>
                    )}

                    <ViewTenderButton
                      onClick={() => handleViewTender(tender.PurchReqId)}
                    >
                      СМОТРЕТЬ ВЕСЬ ТЕНДЕР
                      <img src="/icons/findtender.svg" />
                    </ViewTenderButton>
                  </TenderCard>
                );
              })}
            </TenderResultsGrid>
          )}

          {/* Пагинация */}
          {!isLoading && !error && tenders.length > itemsPerPage && (
            <PaginationContainer>
              {/* Кнопка "Назад" */}
              <PaginationButton
                onClick={handlePrevPage}
                disabled={currentPage === 1}
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10 12L6 8L10 4"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </PaginationButton>

              {/* Номера страниц */}
              {getPageNumbers().map((page, index) => (
                <React.Fragment key={index}>
                  {page === "..." ? (
                    <PaginationDots>...</PaginationDots>
                  ) : (
                    <PaginationButton
                      active={page === currentPage}
                      onClick={() => handlePageChange(page)}
                    >
                      {page}
                    </PaginationButton>
                  )}
                </React.Fragment>
              ))}

              {/* Кнопка "Вперед" */}
              <PaginationButton
                onClick={handleNextPage}
                disabled={currentPage === totalPages}
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 12L10 8L6 4"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </PaginationButton>
            </PaginationContainer>
          )}
        </TenderResultsContainer>
      </ContentSection>
    </FindTenderContainer>
  );
}

export default FindTenderClient;
